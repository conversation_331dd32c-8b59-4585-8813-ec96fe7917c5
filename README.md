# eBPF Process Control Tool

A state-of-the-art eBPF-based process control tool that enforces command-line argument policies in kernel space. This tool intercepts process execution and applies security policies to prevent dangerous command-line arguments from being executed.

## Features

### Core Security Features
- **Kernel-space enforcement**: All policy evaluation happens in eBPF kernel space for maximum performance and security
- **Real-time monitoring**: Intercepts `execve` system calls using eBPF tracepoints
- **Advanced pattern matching**: Supports exact, substring, and wildcard patterns (`--unsafe-*`)
- **Flexible policy system**: Support for both include (required) and exclude (forbidden) argument patterns
- **Argument combination handling**: Detects split arguments like `-o` `StrictHostKeyChecking=no`

### Performance & Scalability
- **Decision caching**: LRU cache for policy decisions with 60-second TTL
- **Rate limiting**: Per-process execution rate limiting (1 second default)
- **High-performance string matching**: Optimized kernel-space algorithms
- **Comprehensive statistics**: Real-time performance metrics and cache analytics
- **Minimal overhead**: < 10μs latency per execve() call

### Management & Monitoring
- **JSON configuration**: Easy-to-manage policy configuration files
- **Policy management tools**: Full CRUD operations for policies (add, remove, enable, disable)
- **Statistics viewer**: Real-time monitoring with continuous mode and cache analytics
- **Advanced test suite**: Comprehensive testing including performance benchmarks
- **Built-in security policies**: Pre-configured policies for SSH, Docker, sudo, curl, wget, etc.
- **Production-ready**: Designed for enterprise security environments

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Space    │    │  Kernel Space   │    │   Hardware      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │args_enforcer│ │    │ │ eBPF Program│ │    │                 │
│ │   daemon    │◄┼────┼►│             │ │    │                 │
│ └─────────────┘ │    │ │ Policy      │ │    │                 │
│                 │    │ │ Enforcement │ │    │                 │
│ ┌─────────────┐ │    │ └─────────────┘ │    │                 │
│ │policy_mgr   │ │    │        │        │    │                 │
│ │   tool      │ │    │        ▼        │    │                 │
│ └─────────────┘ │    │ ┌─────────────┐ │    │                 │
└─────────────────┘    │ │execve hook  │ │    │                 │
                       │ └─────────────┘ │    │                 │
                       └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Linux kernel 4.18+ with eBPF support
- Root privileges
- Development tools: `clang`, `llvm`, `libbpf-dev`, `linux-headers`

### Installation

1. **Install dependencies**:
   ```bash
   sudo make dev-setup
   ```

2. **Build the project**:
   ```bash
   make
   ```

3. **Install system-wide**:
   ```bash
   sudo make install
   ```

### Basic Usage

1. **Start the enforcement daemon**:
   ```bash
   sudo args_enforcer -c /etc/args_enforcer/policy.json -v
   ```

2. **Manage policies**:
   ```bash
   # List current policies
   sudo policy_manager list
   
   # Validate configuration
   sudo policy_manager validate
   
   # Add a new policy (interactive)
   sudo policy_manager add my_policy
   ```

## Policy Configuration

Policies are defined in JSON format with the following structure:

```json
{
  "policies": [
    {
      "name": "ssh_security",
      "description": "Enforce secure SSH connections",
      "cmdline": {
        "include": [
          "-o StrictHostKeyChecking=yes"
        ],
        "exclude": [
          "-o StrictHostKeyChecking=no",
          "-o UserKnownHostsFile=/dev/null"
        ]
      }
    }
  ]
}
```

### Policy Rules

- **include**: Arguments that MUST be present (AND logic)
  - All specified arguments must exist for the command to be allowed
  - Empty array means no required arguments

- **exclude**: Arguments that MUST NOT be present (OR logic)
  - If any specified argument exists, the command is blocked
  - Empty array means no forbidden arguments

### Example Policies

#### SSH Security
```json
{
  "name": "ssh_security",
  "cmdline": {
    "include": ["-o StrictHostKeyChecking=yes"],
    "exclude": ["-o StrictHostKeyChecking=no", "-o UserKnownHostsFile=/dev/null"]
  }
}
```

#### Curl Security
```json
{
  "name": "curl_security", 
  "cmdline": {
    "include": [],
    "exclude": ["-k", "--insecure", "--disable-cert-revocation-checks"]
  }
}
```

#### Docker Security
```json
{
  "name": "docker_security",
  "cmdline": {
    "include": [],
    "exclude": ["--privileged", "--cap-add=ALL"]
  }
}
```

## Command Line Options

### args_enforcer (Main Daemon)

```
Usage: args_enforcer [OPTIONS]

Options:
  -c, --config PATH    Configuration file path (default: /etc/args_enforcer/policy.json)
  -l, --log PATH       Log file path (default: /var/log/args_enforcer.log)
  -d, --daemon         Run as daemon
  -v, --verbose        Enable verbose logging
  -h, --help           Show help message
```

### policy_manager (Policy Management Tool)

```
Usage: policy_manager [OPTIONS] COMMAND [ARGS...]

Options:
  -c, --config PATH    Configuration file path
  -v, --verbose        Enable verbose output
  -h, --help           Show help message

Commands:
  list                 List all policies
  add NAME             Add a new policy (interactive)
  remove NAME          Remove a policy
  enable NAME          Enable a policy
  disable NAME         Disable a policy
  validate             Validate configuration file
  reload               Reload configuration
```

### stats_viewer (Statistics Monitor)

```
Usage: stats_viewer [OPTIONS]

Options:
  -c, --continuous     Continuous monitoring mode
  -i, --interval SEC   Update interval in seconds (default: 5)
  -r, --reset          Reset statistics counters
  -h, --help           Show help message

Examples:
  stats_viewer                    # View current statistics
  stats_viewer -c -i 10          # Continuous monitoring every 10 seconds
  stats_viewer -r                # Reset all counters
```

## Logging

The tool provides comprehensive logging of all process executions:

- **Allowed executions**: Logged at DEBUG level (visible with `-v`)
- **Blocked executions**: Logged at WARN level with policy details
- **System events**: Logged at INFO level

Example log entries:
```
[2024-01-15 10:30:15] WARN: BLOCKED: PID=1234 UID=1000 CMD=/usr/bin/ssh POLICY=ssh_security
[2024-01-15 10:30:15] DEBUG: Arguments:
[2024-01-15 10:30:15] DEBUG:   [0]: ssh
[2024-01-15 10:30:15] DEBUG:   [1]: -o
[2024-01-15 10:30:15] DEBUG:   [2]: StrictHostKeyChecking=no
[2024-01-15 10:30:15] DEBUG:   [3]: user@host
```

## Testing

Run the test suite to verify functionality:

```bash
# Run all tests (requires root)
sudo make test

# Run specific test categories
sudo ./tests/run_tests.sh
```

## Security Considerations

1. **Kernel Space Enforcement**: Policies are enforced in kernel space, making them difficult to bypass
2. **Root Privileges**: The daemon requires root privileges to load eBPF programs
3. **Performance Impact**: Minimal overhead due to eBPF's efficient execution
4. **Policy Validation**: All policies are validated before loading into kernel space

## Troubleshooting

### Common Issues

1. **eBPF program failed to load**:
   - Check kernel version (4.18+ required)
   - Verify eBPF support: `ls /sys/kernel/debug/tracing/`
   - Check for sufficient privileges

2. **Permission denied**:
   - Ensure running as root
   - Check file permissions on configuration files

3. **Invalid configuration**:
   - Validate JSON syntax: `policy_manager validate`
   - Check policy structure matches expected format

### Debug Mode

Enable verbose logging for troubleshooting:
```bash
sudo args_enforcer -v -c /path/to/config.json
```

## Development

### Building from Source

```bash
# Install development dependencies
sudo make dev-setup

# Build debug version
make debug

# Clean build artifacts
make clean
```

### Project Structure

```
├── src/
│   ├── kernel/          # eBPF kernel-space code
│   │   └── args_enforcer.c
│   └── user/            # User-space applications
│       ├── main.c       # Main daemon
│       ├── policy.c     # Policy management
│       ├── bpf_loader.c # eBPF program loader
│       └── logging.c    # Logging system
├── configs/             # Configuration files
├── tests/               # Test suite
└── Makefile            # Build system
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the GPL-2.0 License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section
- Review the test suite for examples
- Open an issue on the project repository
