/*
 * Logging system implementation
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <time.h>
#include <errno.h>
#include <pthread.h>

#include "logging.h"

static FILE *log_file = NULL;
static int log_to_console = 1;
static int verbose_mode = 0;
static pthread_mutex_t log_mutex = PTHREAD_MUTEX_INITIALIZER;

static const char *level_strings[] = {
    "DEBUG",
    "INFO",
    "WARN",
    "ERROR"
};

static const char *level_colors[] = {
    "\033[36m", // Cyan for DEBUG
    "\033[32m", // Green for INFO
    "\033[33m", // Yellow for WARN
    "\033[31m"  // Red for ERROR
};

/* Initialize logging system */
int log_init(const char *log_file_path, int verbose) {
    verbose_mode = verbose;
    
    if (log_file_path) {
        log_file = fopen(log_file_path, "a");
        if (!log_file) {
            fprintf(stderr, "Failed to open log file %s: %s\n", 
                    log_file_path, strerror(errno));
            return -1;
        }
        
        // If we have a log file, don't log to console unless verbose
        log_to_console = verbose;
    }
    
    log_info("Logging system initialized (verbose: %s, file: %s)",
             verbose ? "yes" : "no", log_file_path ? log_file_path : "none");
    
    return 0;
}

/* Cleanup logging system */
void log_cleanup(void) {
    pthread_mutex_lock(&log_mutex);
    
    if (log_file) {
        fclose(log_file);
        log_file = NULL;
    }
    
    pthread_mutex_unlock(&log_mutex);
}

/* Get current timestamp string */
static void get_timestamp(char *buffer, size_t size) {
    time_t now;
    struct tm *tm_info;
    
    time(&now);
    tm_info = localtime(&now);
    
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

/* Log a message */
void log_message(log_level_t level, const char *format, ...) {
    va_list args;
    char timestamp[32];
    char message[1024];
    
    // Skip debug messages unless in verbose mode
    if (level == LOG_LEVEL_DEBUG && !verbose_mode) {
        return;
    }
    
    // Format the message
    va_start(args, format);
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    
    // Get timestamp
    get_timestamp(timestamp, sizeof(timestamp));
    
    pthread_mutex_lock(&log_mutex);
    
    // Log to file
    if (log_file) {
        fprintf(log_file, "[%s] %s: %s\n", timestamp, level_strings[level], message);
        fflush(log_file);
    }
    
    // Log to console
    if (log_to_console) {
        if (isatty(fileno(stderr))) {
            // Use colors if outputting to terminal
            fprintf(stderr, "%s[%s] %s: %s\033[0m\n", 
                    level_colors[level], timestamp, level_strings[level], message);
        } else {
            // No colors for non-terminal output
            fprintf(stderr, "[%s] %s: %s\n", timestamp, level_strings[level], message);
        }
        fflush(stderr);
    }
    
    pthread_mutex_unlock(&log_mutex);
}
