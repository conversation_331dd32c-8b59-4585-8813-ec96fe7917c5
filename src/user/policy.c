/*
 * Policy management implementation
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <json-c/json.h>
#include <bpf/bpf.h>

#include "policy.h"
#include "logging.h"

static struct policy_rule policies[MAX_POLICIES];
static int policy_count = 0;

/* Parse JSON configuration file */
int policy_load_config(const char *config_path) {
    FILE *file;
    char *buffer;
    long file_size;
    json_object *root, *policies_array, *policy_obj;
    json_object *name_obj, *cmdline_obj, *include_obj, *exclude_obj;
    int i, j;
    
    log_info("Loading policy configuration from %s", config_path);
    
    // Read file
    file = fopen(config_path, "r");
    if (!file) {
        log_error("Failed to open config file %s: %s", config_path, strerror(errno));
        return -1;
    }
    
    fseek(file, 0, SEEK_END);
    file_size = ftell(file);
    fseek(file, 0, SEEK_SET);
    
    buffer = malloc(file_size + 1);
    if (!buffer) {
        log_error("Failed to allocate memory for config file");
        fclose(file);
        return -1;
    }
    
    fread(buffer, 1, file_size, file);
    buffer[file_size] = '\0';
    fclose(file);
    
    // Parse JSON
    root = json_tokener_parse(buffer);
    free(buffer);
    
    if (!root) {
        log_error("Failed to parse JSON configuration");
        return -1;
    }
    
    // Get policies array
    if (!json_object_object_get_ex(root, "policies", &policies_array)) {
        log_error("No 'policies' array found in configuration");
        json_object_put(root);
        return -1;
    }
    
    if (!json_object_is_type(policies_array, json_type_array)) {
        log_error("'policies' is not an array");
        json_object_put(root);
        return -1;
    }
    
    // Clear existing policies
    memset(policies, 0, sizeof(policies));
    policy_count = 0;
    
    // Parse each policy
    int array_len = json_object_array_length(policies_array);
    for (i = 0; i < array_len && policy_count < MAX_POLICIES; i++) {
        policy_obj = json_object_array_get_idx(policies_array, i);
        if (!policy_obj) continue;
        
        struct policy_rule *rule = &policies[policy_count];
        
        // Get policy name
        if (json_object_object_get_ex(policy_obj, "name", &name_obj)) {
            const char *name = json_object_get_string(name_obj);
            strncpy(rule->name, name, MAX_POLICY_NAME_LEN - 1);
            rule->name[MAX_POLICY_NAME_LEN - 1] = '\0';
        } else {
            snprintf(rule->name, MAX_POLICY_NAME_LEN, "policy_%d", policy_count);
        }
        
        // Get cmdline object
        if (!json_object_object_get_ex(policy_obj, "cmdline", &cmdline_obj)) {
            log_warn("Policy %s has no 'cmdline' object, skipping", rule->name);
            continue;
        }
        
        // Parse include arguments
        rule->include_count = 0;
        if (json_object_object_get_ex(cmdline_obj, "include", &include_obj) &&
            json_object_is_type(include_obj, json_type_array)) {
            
            int include_len = json_object_array_length(include_obj);
            for (j = 0; j < include_len && rule->include_count < MAX_POLICY_ARGS; j++) {
                json_object *arg_obj = json_object_array_get_idx(include_obj, j);
                if (arg_obj) {
                    const char *arg = json_object_get_string(arg_obj);
                    strncpy(rule->include_args[rule->include_count], arg, MAX_ARG_LEN - 1);
                    rule->include_args[rule->include_count][MAX_ARG_LEN - 1] = '\0';
                    rule->include_count++;
                }
            }
        }
        
        // Parse exclude arguments
        rule->exclude_count = 0;
        if (json_object_object_get_ex(cmdline_obj, "exclude", &exclude_obj) &&
            json_object_is_type(exclude_obj, json_type_array)) {
            
            int exclude_len = json_object_array_length(exclude_obj);
            for (j = 0; j < exclude_len && rule->exclude_count < MAX_POLICY_ARGS; j++) {
                json_object *arg_obj = json_object_array_get_idx(exclude_obj, j);
                if (arg_obj) {
                    const char *arg = json_object_get_string(arg_obj);
                    strncpy(rule->exclude_args[rule->exclude_count], arg, MAX_ARG_LEN - 1);
                    rule->exclude_args[rule->exclude_count][MAX_ARG_LEN - 1] = '\0';
                    rule->exclude_count++;
                }
            }
        }
        
        rule->enabled = 1;
        policy_count++;
        
        log_info("Loaded policy '%s' with %d include and %d exclude rules",
                 rule->name, rule->include_count, rule->exclude_count);
    }
    
    json_object_put(root);
    log_info("Loaded %d policies from configuration", policy_count);
    
    return 0;
}

/* Apply policies to eBPF maps */
int policy_apply_to_bpf(struct bpf_object *obj) {
    struct bpf_map *policy_map;
    int map_fd;
    int i;
    
    log_info("Applying %d policies to eBPF maps", policy_count);
    
    // Find policy map
    policy_map = bpf_object__find_map_by_name(obj, "policy_map");
    if (!policy_map) {
        log_error("Failed to find policy_map in eBPF object");
        return -1;
    }
    
    map_fd = bpf_map__fd(policy_map);
    if (map_fd < 0) {
        log_error("Failed to get policy_map file descriptor");
        return -1;
    }
    
    // Clear existing policies in map
    for (i = 0; i < MAX_POLICIES; i++) {
        unsigned int key = i;
        bpf_map_delete_elem(map_fd, &key);
    }
    
    // Add new policies to map
    for (i = 0; i < policy_count; i++) {
        unsigned int key = i;
        if (bpf_map_update_elem(map_fd, &key, &policies[i], BPF_ANY) != 0) {
            log_error("Failed to update policy map for policy %d: %s", i, strerror(errno));
            return -1;
        }
        
        log_debug("Applied policy '%s' to eBPF map at index %d", policies[i].name, i);
    }
    
    log_info("Successfully applied all policies to eBPF maps");
    return 0;
}

/* Add a new policy rule */
int policy_add_rule(const char *name, const char **include_args, int include_count,
                    const char **exclude_args, int exclude_count) {
    if (policy_count >= MAX_POLICIES) {
        log_error("Maximum number of policies reached");
        return -1;
    }
    
    struct policy_rule *rule = &policies[policy_count];
    memset(rule, 0, sizeof(*rule));
    
    strncpy(rule->name, name, MAX_POLICY_NAME_LEN - 1);
    rule->name[MAX_POLICY_NAME_LEN - 1] = '\0';
    
    // Add include arguments
    rule->include_count = 0;
    for (int i = 0; i < include_count && i < MAX_POLICY_ARGS; i++) {
        strncpy(rule->include_args[i], include_args[i], MAX_ARG_LEN - 1);
        rule->include_args[i][MAX_ARG_LEN - 1] = '\0';
        rule->include_count++;
    }
    
    // Add exclude arguments
    rule->exclude_count = 0;
    for (int i = 0; i < exclude_count && i < MAX_POLICY_ARGS; i++) {
        strncpy(rule->exclude_args[i], exclude_args[i], MAX_ARG_LEN - 1);
        rule->exclude_args[i][MAX_ARG_LEN - 1] = '\0';
        rule->exclude_count++;
    }
    
    rule->enabled = 1;
    policy_count++;
    
    log_info("Added policy '%s'", name);
    return 0;
}

/* List all policy rules */
int policy_list_rules(void) {
    printf("Loaded policies (%d total):\n", policy_count);
    
    for (int i = 0; i < policy_count; i++) {
        struct policy_rule *rule = &policies[i];
        printf("\nPolicy: %s (enabled: %s)\n", rule->name, rule->enabled ? "yes" : "no");
        
        if (rule->include_count > 0) {
            printf("  Include arguments:\n");
            for (int j = 0; j < rule->include_count; j++) {
                printf("    - %s\n", rule->include_args[j]);
            }
        }
        
        if (rule->exclude_count > 0) {
            printf("  Exclude arguments:\n");
            for (int j = 0; j < rule->exclude_count; j++) {
                printf("    - %s\n", rule->exclude_args[j]);
            }
        }
    }
    
    return 0;
}

/* Save configuration to file */
int policy_save_config(const char *config_path) {
    FILE *file;
    json_object *root, *policies_array;
    int i, j;

    log_info("Saving policy configuration to %s", config_path);

    // Create root JSON object
    root = json_object_new_object();
    policies_array = json_object_new_array();

    // Add each policy to the array
    for (i = 0; i < policy_count; i++) {
        struct policy_rule *rule = &policies[i];
        json_object *policy_obj = json_object_new_object();
        json_object *cmdline_obj = json_object_new_object();
        json_object *include_array = json_object_new_array();
        json_object *exclude_array = json_object_new_array();

        // Add policy name
        json_object_object_add(policy_obj, "name", json_object_new_string(rule->name));

        // Add include arguments
        for (j = 0; j < rule->include_count; j++) {
            json_object_array_add(include_array, json_object_new_string(rule->include_args[j]));
        }
        json_object_object_add(cmdline_obj, "include", include_array);

        // Add exclude arguments
        for (j = 0; j < rule->exclude_count; j++) {
            json_object_array_add(exclude_array, json_object_new_string(rule->exclude_args[j]));
        }
        json_object_object_add(cmdline_obj, "exclude", exclude_array);

        json_object_object_add(policy_obj, "cmdline", cmdline_obj);
        json_object_array_add(policies_array, policy_obj);
    }

    json_object_object_add(root, "policies", policies_array);

    // Write to file
    file = fopen(config_path, "w");
    if (!file) {
        log_error("Failed to open config file for writing: %s", strerror(errno));
        json_object_put(root);
        return -1;
    }

    fprintf(file, "%s\n", json_object_to_json_string_ext(root, JSON_C_TO_STRING_PRETTY));
    fclose(file);

    json_object_put(root);
    log_info("Configuration saved successfully");
    return 0;
}

/* Validate configuration file */
int policy_validate_config(const char *config_path) {
    FILE *file;
    char *buffer;
    long file_size;
    json_object *root;

    // Check if file exists and is readable
    file = fopen(config_path, "r");
    if (!file) {
        log_error("Cannot read config file %s: %s", config_path, strerror(errno));
        return -1;
    }

    // Read file content
    fseek(file, 0, SEEK_END);
    file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    buffer = malloc(file_size + 1);
    if (!buffer) {
        log_error("Failed to allocate memory for config validation");
        fclose(file);
        return -1;
    }

    fread(buffer, 1, file_size, file);
    buffer[file_size] = '\0';
    fclose(file);

    // Parse JSON
    root = json_tokener_parse(buffer);
    free(buffer);

    if (!root) {
        log_error("Invalid JSON in configuration file");
        return -1;
    }

    // Basic structure validation
    json_object *policies_array;
    if (!json_object_object_get_ex(root, "policies", &policies_array)) {
        log_error("Missing 'policies' array in configuration");
        json_object_put(root);
        return -1;
    }

    if (!json_object_is_type(policies_array, json_type_array)) {
        log_error("'policies' is not an array");
        json_object_put(root);
        return -1;
    }

    json_object_put(root);
    return 0;
}

/* Remove a policy rule */
int policy_remove_rule(const char *name) {
    int found_index = -1;

    // Find the policy to remove
    for (int i = 0; i < policy_count; i++) {
        if (strcmp(policies[i].name, name) == 0) {
            found_index = i;
            break;
        }
    }

    if (found_index == -1) {
        log_error("Policy '%s' not found", name);
        return -1;
    }

    // Shift remaining policies down
    for (int i = found_index; i < policy_count - 1; i++) {
        policies[i] = policies[i + 1];
    }

    policy_count--;
    memset(&policies[policy_count], 0, sizeof(struct policy_rule));

    log_info("Removed policy '%s'", name);
    return 0;
}

/* Enable a policy rule */
int policy_enable_rule(const char *name) {
    for (int i = 0; i < policy_count; i++) {
        if (strcmp(policies[i].name, name) == 0) {
            policies[i].enabled = 1;
            log_info("Enabled policy '%s'", name);
            return 0;
        }
    }

    log_error("Policy '%s' not found", name);
    return -1;
}

/* Disable a policy rule */
int policy_disable_rule(const char *name) {
    for (int i = 0; i < policy_count; i++) {
        if (strcmp(policies[i].name, name) == 0) {
            policies[i].enabled = 0;
            log_info("Disabled policy '%s'", name);
            return 0;
        }
    }

    log_error("Policy '%s' not found", name);
    return -1;
}

/* Reload configuration */
int policy_reload_config(const char *config_path, struct bpf_object *obj) {
    log_info("Reloading policy configuration");

    // Load new configuration
    if (policy_load_config(config_path) < 0) {
        log_error("Failed to reload configuration");
        return -1;
    }

    // Apply to eBPF maps if object is provided
    if (obj && policy_apply_to_bpf(obj) < 0) {
        log_error("Failed to apply reloaded policies to eBPF maps");
        return -1;
    }

    log_info("Configuration reloaded successfully");
    return 0;
}

/* Cleanup policy resources */
void policy_cleanup(void) {
    policy_count = 0;
    memset(policies, 0, sizeof(policies));
}
