/*
 * Policy management tool - standalone utility for managing policies
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <getopt.h>

#include "policy.h"
#include "logging.h"

#define DEFAULT_CONFIG_PATH "/etc/args_enforcer/policy.json"

static void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS] COMMAND [ARGS...]\n", prog_name);
    printf("\nOptions:\n");
    printf("  -c, --config PATH    Configuration file path (default: %s)\n", DEFAULT_CONFIG_PATH);
    printf("  -v, --verbose        Enable verbose output\n");
    printf("  -h, --help           Show this help message\n");
    printf("\nCommands:\n");
    printf("  list                 List all policies\n");
    printf("  add NAME             Add a new policy (interactive)\n");
    printf("  remove NAME          Remove a policy\n");
    printf("  enable NAME          Enable a policy\n");
    printf("  disable NAME         Disable a policy\n");
    printf("  validate             Validate configuration file\n");
    printf("  reload               Reload configuration (if daemon is running)\n");
    printf("\nExamples:\n");
    printf("  %s list\n", prog_name);
    printf("  %s add ssh_security\n", prog_name);
    printf("  %s remove old_policy\n", prog_name);
    printf("  %s validate\n", prog_name);
}

static int cmd_list(const char *config_path) {
    if (policy_load_config(config_path) < 0) {
        printf("Failed to load configuration from %s\n", config_path);
        return 1;
    }
    
    policy_list_rules();
    return 0;
}

static int cmd_add(const char *config_path, const char *policy_name) {
    char input[1024];
    char *include_args[MAX_POLICY_ARGS];
    char *exclude_args[MAX_POLICY_ARGS];
    int include_count = 0, exclude_count = 0;
    char *token;
    
    printf("Adding new policy: %s\n", policy_name);
    
    // Load existing configuration
    if (policy_load_config(config_path) < 0) {
        printf("Failed to load existing configuration\n");
        return 1;
    }
    
    // Get include arguments
    printf("Enter include arguments (space-separated, empty line to finish):\n");
    if (fgets(input, sizeof(input), stdin)) {
        input[strcspn(input, "\n")] = '\0'; // Remove newline
        
        if (strlen(input) > 0) {
            token = strtok(input, " ");
            while (token && include_count < MAX_POLICY_ARGS) {
                include_args[include_count] = strdup(token);
                include_count++;
                token = strtok(NULL, " ");
            }
        }
    }
    
    // Get exclude arguments
    printf("Enter exclude arguments (space-separated, empty line to finish):\n");
    if (fgets(input, sizeof(input), stdin)) {
        input[strcspn(input, "\n")] = '\0'; // Remove newline
        
        if (strlen(input) > 0) {
            token = strtok(input, " ");
            while (token && exclude_count < MAX_POLICY_ARGS) {
                exclude_args[exclude_count] = strdup(token);
                exclude_count++;
                token = strtok(NULL, " ");
            }
        }
    }
    
    // Add the policy
    if (policy_add_rule(policy_name, (const char **)include_args, include_count,
                        (const char **)exclude_args, exclude_count) < 0) {
        printf("Failed to add policy\n");
        return 1;
    }
    
    // Save configuration
    if (policy_save_config(config_path) < 0) {
        printf("Failed to save configuration\n");
        return 1;
    }
    
    printf("Policy '%s' added successfully\n", policy_name);
    
    // Cleanup
    for (int i = 0; i < include_count; i++) {
        free(include_args[i]);
    }
    for (int i = 0; i < exclude_count; i++) {
        free(exclude_args[i]);
    }
    
    return 0;
}

static int cmd_remove(const char *config_path, const char *policy_name) {
    // Load existing configuration
    if (policy_load_config(config_path) < 0) {
        printf("Failed to load configuration from %s\n", config_path);
        return 1;
    }

    // Remove the policy
    if (policy_remove_rule(policy_name) < 0) {
        printf("Failed to remove policy '%s' (not found)\n", policy_name);
        return 1;
    }

    // Save configuration
    if (policy_save_config(config_path) < 0) {
        printf("Failed to save configuration\n");
        return 1;
    }

    printf("Policy '%s' removed successfully\n", policy_name);
    return 0;
}

static int cmd_enable(const char *config_path, const char *policy_name) {
    // Load existing configuration
    if (policy_load_config(config_path) < 0) {
        printf("Failed to load configuration from %s\n", config_path);
        return 1;
    }

    // Enable the policy
    if (policy_enable_rule(policy_name) < 0) {
        printf("Failed to enable policy '%s' (not found)\n", policy_name);
        return 1;
    }

    // Save configuration
    if (policy_save_config(config_path) < 0) {
        printf("Failed to save configuration\n");
        return 1;
    }

    printf("Policy '%s' enabled successfully\n", policy_name);
    return 0;
}

static int cmd_disable(const char *config_path, const char *policy_name) {
    // Load existing configuration
    if (policy_load_config(config_path) < 0) {
        printf("Failed to load configuration from %s\n", config_path);
        return 1;
    }

    // Disable the policy
    if (policy_disable_rule(policy_name) < 0) {
        printf("Failed to disable policy '%s' (not found)\n", policy_name);
        return 1;
    }

    // Save configuration
    if (policy_save_config(config_path) < 0) {
        printf("Failed to save configuration\n");
        return 1;
    }

    printf("Policy '%s' disabled successfully\n", policy_name);
    return 0;
}

static int cmd_validate(const char *config_path) {
    printf("Validating configuration file: %s\n", config_path);

    if (policy_validate_config(config_path) < 0) {
        printf("Configuration validation failed\n");
        return 1;
    }

    if (policy_load_config(config_path) < 0) {
        printf("Failed to load configuration\n");
        return 1;
    }

    printf("Configuration is valid\n");
    return 0;
}

static int cmd_reload(const char *config_path) {
    // This would send a signal to the running daemon to reload configuration
    // For now, just validate the configuration
    printf("Reload functionality requires daemon integration\n");
    printf("For now, validating configuration...\n");
    return cmd_validate(config_path);
}

int main(int argc, char **argv) {
    const char *config_path = DEFAULT_CONFIG_PATH;
    int verbose = 0;
    int opt;
    
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    // Parse command line options
    while ((opt = getopt_long(argc, argv, "c:vh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'c':
                config_path = optarg;
                break;
            case 'v':
                verbose = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // Initialize logging
    if (log_init(NULL, verbose) < 0) {
        fprintf(stderr, "Failed to initialize logging\n");
        return 1;
    }
    
    // Check if command was provided
    if (optind >= argc) {
        printf("Error: No command specified\n\n");
        print_usage(argv[0]);
        return 1;
    }
    
    const char *command = argv[optind];
    
    // Execute command
    if (strcmp(command, "list") == 0) {
        return cmd_list(config_path);
    } else if (strcmp(command, "add") == 0) {
        if (optind + 1 >= argc) {
            printf("Error: Policy name required for 'add' command\n");
            return 1;
        }
        return cmd_add(config_path, argv[optind + 1]);
    } else if (strcmp(command, "remove") == 0) {
        if (optind + 1 >= argc) {
            printf("Error: Policy name required for 'remove' command\n");
            return 1;
        }
        return cmd_remove(config_path, argv[optind + 1]);
    } else if (strcmp(command, "enable") == 0) {
        if (optind + 1 >= argc) {
            printf("Error: Policy name required for 'enable' command\n");
            return 1;
        }
        return cmd_enable(config_path, argv[optind + 1]);
    } else if (strcmp(command, "disable") == 0) {
        if (optind + 1 >= argc) {
            printf("Error: Policy name required for 'disable' command\n");
            return 1;
        }
        return cmd_disable(config_path, argv[optind + 1]);
    } else if (strcmp(command, "validate") == 0) {
        return cmd_validate(config_path);
    } else if (strcmp(command, "reload") == 0) {
        return cmd_reload(config_path);
    } else {
        printf("Error: Unknown command '%s'\n\n", command);
        print_usage(argv[0]);
        return 1;
    }
    
    return 0;
}
