/*
 * Statistics viewer for eBPF Process Control Tool
 * Displays runtime statistics from the eBPF program
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

#include "logging.h"

#define BPF_PROGRAM_PATH "build/args_enforcer.o"

/* Statistics structure matching kernel definition */
struct stats {
    unsigned long long total_execve_calls;
    unsigned long long blocked_executions;
    unsigned long long allowed_executions;
    unsigned long long policy_evaluations;
    unsigned long long cache_hits;
    unsigned long long cache_misses;
};

static void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS]\n", prog_name);
    printf("Options:\n");
    printf("  -c, --continuous     Continuous monitoring mode\n");
    printf("  -i, --interval SEC   Update interval in seconds (default: 5)\n");
    printf("  -r, --reset          Reset statistics counters\n");
    printf("  -h, --help           Show this help message\n");
    printf("\neBPF Process Control Tool - Statistics Viewer\n");
}

static void print_stats(struct stats *stats) {
    time_t now;
    struct tm *tm_info;
    char timestamp[32];
    
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);
    
    printf("\n=== eBPF Process Control Statistics [%s] ===\n", timestamp);
    printf("Total execve() calls:     %llu\n", stats->total_execve_calls);
    printf("Blocked executions:       %llu\n", stats->blocked_executions);
    printf("Allowed executions:       %llu\n", stats->allowed_executions);
    printf("Policy evaluations:       %llu\n", stats->policy_evaluations);
    printf("Cache hits:               %llu\n", stats->cache_hits);
    printf("Cache misses:             %llu\n", stats->cache_misses);

    if (stats->total_execve_calls > 0) {
        double block_rate = (double)stats->blocked_executions / stats->total_execve_calls * 100.0;
        double avg_policies = (double)stats->policy_evaluations / stats->total_execve_calls;

        printf("Block rate:               %.2f%%\n", block_rate);
        printf("Avg policies per exec:    %.2f\n", avg_policies);

        unsigned long long total_cache = stats->cache_hits + stats->cache_misses;
        if (total_cache > 0) {
            double cache_hit_rate = (double)stats->cache_hits / total_cache * 100.0;
            printf("Cache hit rate:           %.2f%%\n", cache_hit_rate);
        }
    }
    
    printf("================================================\n");
}

static int get_stats_map_fd(void) {
    struct bpf_object *obj;
    struct bpf_map *stats_map;
    int map_fd;
    
    // Check if object file exists
    if (access(BPF_PROGRAM_PATH, R_OK) != 0) {
        fprintf(stderr, "eBPF object file not found: %s\n", BPF_PROGRAM_PATH);
        fprintf(stderr, "Make sure the args_enforcer daemon is running\n");
        return -1;
    }
    
    // Open eBPF object file
    obj = bpf_object__open(BPF_PROGRAM_PATH);
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "Failed to open eBPF object file: %s\n", strerror(errno));
        return -1;
    }
    
    // Find stats map
    stats_map = bpf_object__find_map_by_name(obj, "stats_map");
    if (!stats_map) {
        fprintf(stderr, "Failed to find stats_map in eBPF object\n");
        bpf_object__close(obj);
        return -1;
    }
    
    map_fd = bpf_map__fd(stats_map);
    if (map_fd < 0) {
        fprintf(stderr, "Failed to get stats_map file descriptor\n");
        bpf_object__close(obj);
        return -1;
    }
    
    // Note: We don't close the object here as we need the map fd
    return map_fd;
}

static int read_stats(int map_fd, struct stats *stats) {
    unsigned int key = 0;
    
    if (bpf_map_lookup_elem(map_fd, &key, stats) != 0) {
        fprintf(stderr, "Failed to read statistics: %s\n", strerror(errno));
        return -1;
    }
    
    return 0;
}

static int reset_stats(int map_fd) {
    unsigned int key = 0;
    struct stats zero_stats = {0};
    
    if (bpf_map_update_elem(map_fd, &key, &zero_stats, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to reset statistics: %s\n", strerror(errno));
        return -1;
    }
    
    printf("Statistics counters reset successfully\n");
    return 0;
}

int main(int argc, char **argv) {
    int continuous = 0;
    int interval = 5;
    int reset = 0;
    int opt;
    int map_fd;
    struct stats stats;
    
    // Parse command line arguments
    while ((opt = getopt(argc, argv, "ci:rh")) != -1) {
        switch (opt) {
            case 'c':
                continuous = 1;
                break;
            case 'i':
                interval = atoi(optarg);
                if (interval <= 0) {
                    fprintf(stderr, "Invalid interval: %s\n", optarg);
                    return 1;
                }
                break;
            case 'r':
                reset = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // Get stats map file descriptor
    map_fd = get_stats_map_fd();
    if (map_fd < 0) {
        return 1;
    }
    
    // Reset statistics if requested
    if (reset) {
        return reset_stats(map_fd);
    }
    
    // Continuous monitoring mode
    if (continuous) {
        printf("Starting continuous monitoring (interval: %d seconds)\n", interval);
        printf("Press Ctrl+C to stop\n");
        
        while (1) {
            if (read_stats(map_fd, &stats) == 0) {
                system("clear");  // Clear screen
                print_stats(&stats);
            }
            sleep(interval);
        }
    } else {
        // Single shot mode
        if (read_stats(map_fd, &stats) == 0) {
            print_stats(&stats);
        }
    }
    
    return 0;
}
