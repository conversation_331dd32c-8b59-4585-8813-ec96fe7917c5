/*
 * eBPF Process Control Tool - User Space Main
 * Command-line argument policy enforcement daemon
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

#include "policy.h"
#include "bpf_loader.h"
#include "logging.h"

#define DEFAULT_CONFIG_PATH "/etc/args_enforcer/policy.json"
#define DEFAULT_LOG_PATH "/var/log/args_enforcer.log"

static volatile int running = 1;
static struct bpf_object *obj = NULL;
static int events_fd = -1;

/* Signal handler for graceful shutdown */
static void signal_handler(int sig) {
    log_info("Received signal %d, shutting down...", sig);
    running = 0;
}

/* Print usage information */
static void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS]\n", prog_name);
    printf("Options:\n");
    printf("  -c, --config PATH    Configuration file path (default: %s)\n", DEFAULT_CONFIG_PATH);
    printf("  -l, --log PATH       Log file path (default: %s)\n", DEFAULT_LOG_PATH);
    printf("  -d, --daemon         Run as daemon\n");
    printf("  -v, --verbose        Enable verbose logging\n");
    printf("  -h, --help           Show this help message\n");
    printf("\neBPF Process Control Tool - Command-line argument policy enforcement\n");
}

/* Daemonize the process */
static int daemonize(void) {
    pid_t pid = fork();
    
    if (pid < 0) {
        log_error("Failed to fork: %s", strerror(errno));
        return -1;
    }
    
    if (pid > 0) {
        exit(0); // Parent exits
    }
    
    // Child continues
    if (setsid() < 0) {
        log_error("Failed to create new session: %s", strerror(errno));
        return -1;
    }
    
    // Fork again to ensure we're not session leader
    pid = fork();
    if (pid < 0) {
        log_error("Failed to fork second time: %s", strerror(errno));
        return -1;
    }
    
    if (pid > 0) {
        exit(0); // First child exits
    }
    
    // Change working directory to root
    if (chdir("/") < 0) {
        log_error("Failed to change directory to /: %s", strerror(errno));
        return -1;
    }
    
    // Close standard file descriptors
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    return 0;
}

/* Handle events from eBPF program */
static int handle_event(void *ctx, void *data, size_t data_sz) {
    struct exec_event *event = (struct exec_event *)data;
    
    if (event->action == 1) {
        // Blocked execution
        log_warn("BLOCKED: PID=%d UID=%d CMD=%s POLICY=%s", 
                 event->pid, event->uid, event->filename, event->policy_matched);
        
        // Log arguments for debugging
        log_debug("Arguments:");
        for (int i = 0; i < event->argc && i < MAX_ARGS; i++) {
            log_debug("  [%d]: %s", i, event->args[i]);
        }
    } else {
        // Allowed execution (only log in verbose mode)
        log_debug("ALLOWED: PID=%d UID=%d CMD=%s", 
                  event->pid, event->uid, event->filename);
    }
    
    return 0;
}

/* Main event loop */
static int run_event_loop(void) {
    struct ring_buffer *rb;
    int err;
    
    // Create ring buffer for events
    rb = ring_buffer__new(events_fd, handle_event, NULL, NULL);
    if (!rb) {
        log_error("Failed to create ring buffer");
        return -1;
    }
    
    log_info("eBPF process control tool started, monitoring execve calls...");
    
    while (running) {
        err = ring_buffer__poll(rb, 100); // 100ms timeout
        if (err == -EINTR) {
            continue;
        }
        if (err < 0) {
            log_error("Error polling ring buffer: %d", err);
            break;
        }
    }
    
    ring_buffer__free(rb);
    return 0;
}

int main(int argc, char **argv) {
    const char *config_path = DEFAULT_CONFIG_PATH;
    const char *log_path = DEFAULT_LOG_PATH;
    int daemon_mode = 0;
    int verbose = 0;
    int opt;
    
    // Parse command line arguments
    while ((opt = getopt(argc, argv, "c:l:dvh")) != -1) {
        switch (opt) {
            case 'c':
                config_path = optarg;
                break;
            case 'l':
                log_path = optarg;
                break;
            case 'd':
                daemon_mode = 1;
                break;
            case 'v':
                verbose = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // Initialize logging
    if (log_init(log_path, verbose) < 0) {
        fprintf(stderr, "Failed to initialize logging\n");
        return 1;
    }
    
    log_info("Starting eBPF process control tool");
    log_info("Config: %s, Log: %s, Daemon: %s", 
             config_path, log_path, daemon_mode ? "yes" : "no");
    
    // Check if running as root
    if (geteuid() != 0) {
        log_error("This program must be run as root");
        return 1;
    }
    
    // Increase RLIMIT_MEMLOCK for eBPF
    struct rlimit rlim = {
        .rlim_cur = RLIM_INFINITY,
        .rlim_max = RLIM_INFINITY,
    };
    if (setrlimit(RLIMIT_MEMLOCK, &rlim)) {
        log_error("Failed to increase RLIMIT_MEMLOCK: %s", strerror(errno));
        return 1;
    }
    
    // Load and parse policy configuration
    if (policy_load_config(config_path) < 0) {
        log_error("Failed to load policy configuration");
        return 1;
    }
    
    // Load eBPF program
    if (bpf_load_program(&obj, &events_fd) < 0) {
        log_error("Failed to load eBPF program");
        return 1;
    }
    
    // Apply policies to eBPF maps
    if (policy_apply_to_bpf(obj) < 0) {
        log_error("Failed to apply policies to eBPF maps");
        goto cleanup;
    }
    
    // Daemonize if requested
    if (daemon_mode) {
        if (daemonize() < 0) {
            log_error("Failed to daemonize");
            goto cleanup;
        }
    }
    
    // Set up signal handlers
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGHUP, signal_handler);
    
    // Run main event loop
    run_event_loop();
    
cleanup:
    log_info("Cleaning up and exiting");
    
    if (obj) {
        bpf_object__close(obj);
    }
    
    policy_cleanup();
    log_cleanup();
    
    return 0;
}
