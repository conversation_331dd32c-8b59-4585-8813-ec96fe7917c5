/*
 * Policy management header
 */

#ifndef POLICY_H
#define P<PERSON><PERSON><PERSON>_H

#include <bpf/libbpf.h>

#define MAX_ARGS 64
#define MAX_ARG_LEN 256
#define MAX_POLICIES 100
#define MAX_POLICY_ARGS 32
#define MAX_POLICY_NAME_LEN 64

/* Policy structure matching kernel-space definition */
struct policy_rule {
    char name[MAX_POLICY_NAME_LEN];
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
};

/* Event structure matching kernel-space definition */
struct exec_event {
    unsigned int pid;
    unsigned int ppid;
    unsigned int uid;
    unsigned int gid;
    char comm[16];
    char filename[256];
    char args[MAX_ARGS][MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
};

/* Policy management functions */
int policy_load_config(const char *config_path);
int policy_apply_to_bpf(struct bpf_object *obj);
int policy_reload_config(const char *config_path, struct bpf_object *obj);
void policy_cleanup(void);

/* Policy manipulation functions */
int policy_add_rule(const char *name, const char **include_args, int include_count,
                    const char **exclude_args, int exclude_count);
int policy_remove_rule(const char *name);
int policy_enable_rule(const char *name);
int policy_disable_rule(const char *name);
int policy_list_rules(void);

/* Configuration file functions */
int policy_save_config(const char *config_path);
int policy_validate_config(const char *config_path);

#endif /* POLICY_H */
