// SPDX-License-Identifier: GPL-2.0
/*
 * eBPF Process Control Tool - Kernel Space
 * Command-line argument policy enforcement in eBPF
 */

#include <linux/bpf.h>
#include <linux/ptrace.h>
#include <linux/sched.h>
#include <linux/string.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_ARGS 64
#define MAX_ARG_LEN 256
#define MAX_POLICIES 100
#define MAX_POLICY_ARGS 32
#define MAX_POLICY_NAME_LEN 64

/* Policy structure for eBPF maps */
struct policy_rule {
    char name[MAX_POLICY_NAME_LEN];
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
};

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[16];
    char filename[256];
    char args[MAX_ARGS][MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
};

/* BPF Maps */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, __u32);
    __type(value, struct policy_rule);
} policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);
    __type(value, __u32);
} blocked_pids SEC(".maps");

/* Helper function to compare strings */
static __always_inline int bpf_strcmp(const char *s1, const char *s2, int max_len) {
    for (int i = 0; i < max_len; i++) {
        if (s1[i] != s2[i])
            return s1[i] - s2[i];
        if (s1[i] == '\0')
            return 0;
    }
    return 0;
}

/* Helper function to find substring in string */
static __always_inline int bpf_strstr(const char *haystack, const char *needle, int haystack_len, int needle_len) {
    if (needle_len == 0) return 1;
    if (needle_len > haystack_len) return 0;
    
    for (int i = 0; i <= haystack_len - needle_len; i++) {
        int match = 1;
        for (int j = 0; j < needle_len; j++) {
            if (haystack[i + j] != needle[j]) {
                match = 0;
                break;
            }
        }
        if (match) return 1;
    }
    return 0;
}

/* Check if argument exists in argument list */
static __always_inline int check_arg_exists(char args[][MAX_ARG_LEN], int argc, const char *target_arg) {
    for (int i = 0; i < argc && i < MAX_ARGS; i++) {
        if (bpf_strstr(args[i], target_arg, MAX_ARG_LEN, MAX_ARG_LEN)) {
            return 1;
        }
    }
    return 0;
}

/* Evaluate policy against arguments */
static __always_inline int evaluate_policy(struct policy_rule *policy, char args[][MAX_ARG_LEN], int argc) {
    // Check include rules (AND logic - all must be present)
    for (int i = 0; i < policy->include_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->include_args[i][0] != '\0') {
            if (!check_arg_exists(args, argc, policy->include_args[i])) {
                return 1; // Block - required argument missing
            }
        }
    }
    
    // Check exclude rules (OR logic - any present blocks execution)
    for (int i = 0; i < policy->exclude_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->exclude_args[i][0] != '\0') {
            if (check_arg_exists(args, argc, policy->exclude_args[i])) {
                return 1; // Block - forbidden argument present
            }
        }
    }
    
    return 0; // Allow
}

/* Parse command line arguments from execve */
static __always_inline int parse_args(struct pt_regs *ctx, char args[][MAX_ARG_LEN]) {
    const char __user *const __user *argv;
    const char __user *arg;
    int argc = 0;
    
    argv = (const char __user *const __user *)PT_REGS_PARM2(ctx);
    
    for (int i = 0; i < MAX_ARGS; i++) {
        if (bpf_probe_read_user(&arg, sizeof(arg), &argv[i]) != 0)
            break;
        
        if (!arg)
            break;
            
        if (bpf_probe_read_user_str(args[i], MAX_ARG_LEN, arg) < 0)
            break;
            
        argc++;
    }
    
    return argc;
}

SEC("tracepoint/syscalls/sys_enter_execve")
int trace_execve(struct trace_event_raw_sys_enter *ctx) {
    struct exec_event *event;
    struct policy_rule *policy;
    struct task_struct *task;
    __u32 pid, uid, gid;
    int argc, action = 0;
    char args[MAX_ARGS][MAX_ARG_LEN];
    char filename[256];
    
    // Get current task info
    task = (struct task_struct *)bpf_get_current_task();
    pid = bpf_get_current_pid_tgid() >> 32;
    uid = bpf_get_current_uid_gid() & 0xffffffff;
    gid = bpf_get_current_uid_gid() >> 32;
    
    // Parse filename
    const char __user *filename_ptr = (const char __user *)ctx->args[0];
    if (bpf_probe_read_user_str(filename, sizeof(filename), filename_ptr) < 0)
        return 0;
    
    // Parse arguments
    argc = parse_args((struct pt_regs *)ctx, args);
    
    // Check policies
    for (__u32 policy_id = 0; policy_id < MAX_POLICIES; policy_id++) {
        policy = bpf_map_lookup_elem(&policy_map, &policy_id);
        if (!policy || !policy->enabled)
            continue;
            
        if (evaluate_policy(policy, args, argc)) {
            action = 1; // Block
            
            // Add to blocked PIDs map
            __u32 blocked = 1;
            bpf_map_update_elem(&blocked_pids, &pid, &blocked, BPF_ANY);
            
            // Send event to user space
            event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
            if (event) {
                event->pid = pid;
                event->ppid = BPF_CORE_READ(task, real_parent, pid);
                event->uid = uid;
                event->gid = gid;
                event->argc = argc;
                event->action = action;
                
                bpf_get_current_comm(event->comm, sizeof(event->comm));
                bpf_probe_read_kernel_str(event->filename, sizeof(event->filename), filename);
                bpf_probe_read_kernel_str(event->policy_matched, sizeof(event->policy_matched), policy->name);
                
                // Copy arguments
                for (int i = 0; i < argc && i < MAX_ARGS; i++) {
                    bpf_probe_read_kernel_str(event->args[i], MAX_ARG_LEN, args[i]);
                }
                
                bpf_ringbuf_submit(event, 0);
            }
            
            return -1; // Block execution
        }
    }
    
    // Send allow event to user space for logging
    event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
    if (event) {
        event->pid = pid;
        event->ppid = BPF_CORE_READ(task, real_parent, pid);
        event->uid = uid;
        event->gid = gid;
        event->argc = argc;
        event->action = 0; // Allow
        
        bpf_get_current_comm(event->comm, sizeof(event->comm));
        bpf_probe_read_kernel_str(event->filename, sizeof(event->filename), filename);
        event->policy_matched[0] = '\0';
        
        // Copy arguments
        for (int i = 0; i < argc && i < MAX_ARGS; i++) {
            bpf_probe_read_kernel_str(event->args[i], MAX_ARG_LEN, args[i]);
        }
        
        bpf_ringbuf_submit(event, 0);
    }
    
    return 0; // Allow execution
}

char _license[] SEC("license") = "GPL";
