#!/bin/bash

# eBPF Process Control Tool - Test Suite
# Basic functionality tests

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BIN_DIR="$PROJECT_DIR/bin"
CONFIG_DIR="$PROJECT_DIR/configs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

test_passed() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

test_failed() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "Tests must be run as root (required for eBPF)"
        exit 1
    fi
}

# Check if binaries exist
check_binaries() {
    log_info "Checking if binaries exist..."
    
    if [[ ! -f "$BIN_DIR/args_enforcer" ]]; then
        log_error "args_enforcer binary not found. Run 'make' first."
        exit 1
    fi
    
    if [[ ! -f "$BIN_DIR/policy_manager" ]]; then
        log_error "policy_manager binary not found. Run 'make' first."
        exit 1
    fi
    
    test_passed "Binaries exist"
}

# Test policy configuration validation
test_policy_validation() {
    log_info "Testing policy configuration validation..."
    
    # Test valid configuration
    if "$BIN_DIR/policy_manager" -c "$CONFIG_DIR/default_policy.json" validate; then
        test_passed "Valid configuration accepted"
    else
        test_failed "Valid configuration rejected"
    fi
    
    # Test invalid configuration
    echo '{"invalid": "json"}' > /tmp/invalid_policy.json
    if ! "$BIN_DIR/policy_manager" -c /tmp/invalid_policy.json validate 2>/dev/null; then
        test_passed "Invalid configuration rejected"
    else
        test_failed "Invalid configuration accepted"
    fi
    rm -f /tmp/invalid_policy.json
}

# Test policy listing
test_policy_listing() {
    log_info "Testing policy listing..."
    
    if "$BIN_DIR/policy_manager" -c "$CONFIG_DIR/default_policy.json" list > /dev/null; then
        test_passed "Policy listing works"
    else
        test_failed "Policy listing failed"
    fi
}

# Test eBPF program loading (without attaching)
test_ebpf_loading() {
    log_info "Testing eBPF program loading..."
    
    # Create a test configuration
    cat > /tmp/test_policy.json << EOF
{
  "policies": [
    {
      "name": "test_policy",
      "cmdline": {
        "include": [],
        "exclude": ["--test-forbidden"]
      }
    }
  ]
}
EOF
    
    # Try to start the enforcer for a short time
    timeout 5s "$BIN_DIR/args_enforcer" -c /tmp/test_policy.json -v &
    ENFORCER_PID=$!
    
    sleep 2
    
    if kill -0 $ENFORCER_PID 2>/dev/null; then
        test_passed "eBPF program loaded successfully"
        kill $ENFORCER_PID 2>/dev/null || true
        wait $ENFORCER_PID 2>/dev/null || true
    else
        test_failed "eBPF program failed to load"
    fi
    
    rm -f /tmp/test_policy.json
}

# Test basic functionality with a simple command
test_basic_functionality() {
    log_info "Testing basic functionality..."
    
    # Create a test policy that blocks 'echo --forbidden'
    cat > /tmp/test_policy.json << EOF
{
  "policies": [
    {
      "name": "echo_test",
      "cmdline": {
        "include": [],
        "exclude": ["--forbidden"]
      }
    }
  ]
}
EOF
    
    # Start the enforcer in background
    "$BIN_DIR/args_enforcer" -c /tmp/test_policy.json -v > /tmp/enforcer.log 2>&1 &
    ENFORCER_PID=$!
    
    sleep 3
    
    # Test allowed command
    if echo "allowed" > /dev/null 2>&1; then
        test_passed "Allowed command executed"
    else
        test_failed "Allowed command blocked"
    fi
    
    # Note: Testing blocked commands is complex as it requires the eBPF program
    # to actually intercept execve calls, which may not work in all test environments
    
    # Cleanup
    kill $ENFORCER_PID 2>/dev/null || true
    wait $ENFORCER_PID 2>/dev/null || true
    rm -f /tmp/test_policy.json /tmp/enforcer.log
    
    test_passed "Basic functionality test completed"
}

# Test configuration file creation
test_config_creation() {
    log_info "Testing configuration file creation..."
    
    # Test creating a new policy (non-interactive)
    if [[ -f "$CONFIG_DIR/default_policy.json" ]]; then
        test_passed "Default configuration exists"
    else
        test_failed "Default configuration missing"
    fi
}

# Main test execution
main() {
    log_info "Starting eBPF Process Control Tool test suite..."
    
    check_root
    check_binaries
    test_policy_validation
    test_policy_listing
    test_config_creation
    
    # Only run eBPF tests if we have the necessary kernel features
    if [[ -d /sys/kernel/debug/tracing ]]; then
        test_ebpf_loading
        test_basic_functionality
    else
        log_warn "Skipping eBPF tests - tracing not available"
    fi
    
    # Print results
    echo
    log_info "Test Results:"
    echo "  Passed: $TESTS_PASSED"
    echo "  Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_info "All tests passed!"
        exit 0
    else
        log_error "Some tests failed!"
        exit 1
    fi
}

main "$@"
