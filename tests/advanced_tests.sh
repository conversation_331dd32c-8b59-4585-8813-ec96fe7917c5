#!/bin/bash

# eBPF Process Control Tool - Advanced Test Suite
# Comprehensive testing of policy enforcement and advanced features

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BIN_DIR="$PROJECT_DIR/bin"
CONFIG_DIR="$PROJECT_DIR/configs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

test_passed() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

test_failed() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "Tests must be run as root (required for eBPF)"
        exit 1
    fi
}

# Check if all binaries exist
check_binaries() {
    log_info "Checking if all binaries exist..."
    
    local binaries=("args_enforcer" "policy_manager" "stats_viewer")
    
    for binary in "${binaries[@]}"; do
        if [[ ! -f "$BIN_DIR/$binary" ]]; then
            log_error "$binary binary not found. Run 'make' first."
            exit 1
        fi
    done
    
    test_passed "All binaries exist"
}

# Test advanced policy configuration
test_advanced_policies() {
    log_test "Testing advanced policy configuration..."
    
    # Test advanced policies validation
    if "$BIN_DIR/policy_manager" -c "$CONFIG_DIR/advanced_policies.json" validate; then
        test_passed "Advanced policies configuration is valid"
    else
        test_failed "Advanced policies configuration is invalid"
    fi
    
    # Test policy listing with advanced config
    if "$BIN_DIR/policy_manager" -c "$CONFIG_DIR/advanced_policies.json" list > /dev/null; then
        test_passed "Advanced policies listing works"
    else
        test_failed "Advanced policies listing failed"
    fi
}

# Test policy management operations
test_policy_management() {
    log_test "Testing policy management operations..."
    
    # Create a temporary config for testing
    local test_config="/tmp/test_policy_mgmt.json"
    cp "$CONFIG_DIR/default_policy.json" "$test_config"
    
    # Test adding a policy
    echo -e "\n\n" | "$BIN_DIR/policy_manager" -c "$test_config" add test_policy > /dev/null 2>&1
    if "$BIN_DIR/policy_manager" -c "$test_config" list | grep -q "test_policy"; then
        test_passed "Policy addition works"
    else
        test_failed "Policy addition failed"
    fi
    
    # Test removing a policy
    if "$BIN_DIR/policy_manager" -c "$test_config" remove test_policy > /dev/null 2>&1; then
        if ! "$BIN_DIR/policy_manager" -c "$test_config" list | grep -q "test_policy"; then
            test_passed "Policy removal works"
        else
            test_failed "Policy removal failed"
        fi
    else
        test_failed "Policy removal command failed"
    fi
    
    rm -f "$test_config"
}

# Test statistics functionality
test_statistics() {
    log_test "Testing statistics functionality..."
    
    # Create a minimal test policy
    cat > /tmp/stats_test_policy.json << EOF
{
  "policies": [
    {
      "name": "stats_test",
      "cmdline": {
        "include": [],
        "exclude": ["--never-match-this-argument"]
      }
    }
  ]
}
EOF
    
    # Start the enforcer in background
    "$BIN_DIR/args_enforcer" -c /tmp/stats_test_policy.json > /tmp/enforcer_stats.log 2>&1 &
    local enforcer_pid=$!
    
    sleep 3
    
    # Check if stats viewer can read statistics
    if "$BIN_DIR/stats_viewer" > /dev/null 2>&1; then
        test_passed "Statistics viewer works"
    else
        test_failed "Statistics viewer failed"
    fi
    
    # Test statistics reset
    if "$BIN_DIR/stats_viewer" -r > /dev/null 2>&1; then
        test_passed "Statistics reset works"
    else
        test_failed "Statistics reset failed"
    fi
    
    # Cleanup
    kill $enforcer_pid 2>/dev/null || true
    wait $enforcer_pid 2>/dev/null || true
    rm -f /tmp/stats_test_policy.json /tmp/enforcer_stats.log
}

# Test wildcard pattern matching
test_wildcard_patterns() {
    log_test "Testing wildcard pattern matching..."
    
    # Create a policy with wildcard patterns
    cat > /tmp/wildcard_test_policy.json << EOF
{
  "policies": [
    {
      "name": "wildcard_test",
      "cmdline": {
        "include": [],
        "exclude": ["--unsafe-*", "--debug-*"]
      }
    }
  ]
}
EOF
    
    # Start the enforcer
    "$BIN_DIR/args_enforcer" -c /tmp/wildcard_test_policy.json > /tmp/wildcard_test.log 2>&1 &
    local enforcer_pid=$!
    
    sleep 3
    
    # This test is conceptual - in a real environment, we would test actual command blocking
    # For now, we just verify the configuration loads correctly
    if kill -0 $enforcer_pid 2>/dev/null; then
        test_passed "Wildcard pattern policy loads successfully"
    else
        test_failed "Wildcard pattern policy failed to load"
    fi
    
    # Cleanup
    kill $enforcer_pid 2>/dev/null || true
    wait $enforcer_pid 2>/dev/null || true
    rm -f /tmp/wildcard_test_policy.json /tmp/wildcard_test.log
}

# Test configuration validation edge cases
test_config_validation() {
    log_test "Testing configuration validation edge cases..."
    
    # Test empty policies array
    echo '{"policies": []}' > /tmp/empty_policies.json
    if "$BIN_DIR/policy_manager" -c /tmp/empty_policies.json validate > /dev/null 2>&1; then
        test_passed "Empty policies array validation works"
    else
        test_failed "Empty policies array validation failed"
    fi
    
    # Test malformed JSON
    echo '{"policies": [' > /tmp/malformed.json
    if ! "$BIN_DIR/policy_manager" -c /tmp/malformed.json validate > /dev/null 2>&1; then
        test_passed "Malformed JSON properly rejected"
    else
        test_failed "Malformed JSON incorrectly accepted"
    fi
    
    # Test missing cmdline object
    cat > /tmp/missing_cmdline.json << EOF
{
  "policies": [
    {
      "name": "test_policy"
    }
  ]
}
EOF
    
    if "$BIN_DIR/policy_manager" -c /tmp/missing_cmdline.json validate > /dev/null 2>&1; then
        test_passed "Missing cmdline object handled gracefully"
    else
        test_failed "Missing cmdline object not handled properly"
    fi
    
    # Cleanup
    rm -f /tmp/empty_policies.json /tmp/malformed.json /tmp/missing_cmdline.json
}

# Test performance with many policies
test_performance() {
    log_test "Testing performance with many policies..."
    
    # Create a config with many policies
    cat > /tmp/many_policies.json << 'EOF'
{
  "policies": [
EOF
    
    # Add 50 test policies
    for i in {1..50}; do
        cat >> /tmp/many_policies.json << EOF
    {
      "name": "test_policy_$i",
      "cmdline": {
        "include": [],
        "exclude": ["--test-arg-$i"]
      }
    }$([ $i -lt 50 ] && echo ",")
EOF
    done
    
    echo '  ]' >> /tmp/many_policies.json
    echo '}' >> /tmp/many_policies.json
    
    # Test loading many policies
    start_time=$(date +%s.%N)
    if "$BIN_DIR/policy_manager" -c /tmp/many_policies.json validate > /dev/null 2>&1; then
        end_time=$(date +%s.%N)
        duration=$(echo "$end_time - $start_time" | bc -l)
        test_passed "Many policies validation completed in ${duration}s"
    else
        test_failed "Many policies validation failed"
    fi
    
    rm -f /tmp/many_policies.json
}

# Main test execution
main() {
    log_info "Starting eBPF Process Control Tool advanced test suite..."
    
    check_root
    check_binaries
    test_advanced_policies
    test_policy_management
    test_config_validation
    test_performance
    
    # Only run eBPF-dependent tests if we have the necessary kernel features
    if [[ -d /sys/kernel/debug/tracing ]]; then
        test_statistics
        test_wildcard_patterns
    else
        log_warn "Skipping eBPF-dependent tests - tracing not available"
    fi
    
    # Print results
    echo
    log_info "Advanced Test Results:"
    echo "  Passed: $TESTS_PASSED"
    echo "  Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_info "All advanced tests passed!"
        exit 0
    else
        log_error "Some advanced tests failed!"
        exit 1
    fi
}

main "$@"
