#!/bin/bash

# eBPF Process Control Tool - Packaging Script
# Creates distribution packages for the project

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
VERSION="1.0.0"
PACKAGE_NAME="ebpf-process-control"
BUILD_DIR="$PROJECT_DIR/build"
DIST_DIR="$PROJECT_DIR/dist"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Clean previous builds
clean_build() {
    log_step "Cleaning previous builds..."
    rm -rf "$BUILD_DIR" "$DIST_DIR"
    mkdir -p "$BUILD_DIR" "$DIST_DIR"
}

# Build the project
build_project() {
    log_step "Building project..."
    cd "$PROJECT_DIR"
    
    if ! make clean; then
        log_error "Failed to clean project"
        exit 1
    fi
    
    if ! make all; then
        log_error "Failed to build project"
        exit 1
    fi
    
    log_info "Build completed successfully"
}

# Create source package
create_source_package() {
    log_step "Creating source package..."
    
    local src_package_dir="$BUILD_DIR/${PACKAGE_NAME}-${VERSION}-src"
    mkdir -p "$src_package_dir"
    
    # Copy source files
    cp -r "$PROJECT_DIR/src" "$src_package_dir/"
    cp -r "$PROJECT_DIR/configs" "$src_package_dir/"
    cp -r "$PROJECT_DIR/tests" "$src_package_dir/"
    cp -r "$PROJECT_DIR/docs" "$src_package_dir/"
    cp -r "$PROJECT_DIR/scripts" "$src_package_dir/"
    
    # Copy build files
    cp "$PROJECT_DIR/Makefile" "$src_package_dir/"
    cp "$PROJECT_DIR/README.md" "$src_package_dir/"
    cp "$PROJECT_DIR/LICENSE" "$src_package_dir/" 2>/dev/null || true
    
    # Create installation script
    cat > "$src_package_dir/install.sh" << 'EOF'
#!/bin/bash
set -e

echo "Installing eBPF Process Control Tool..."

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    echo "Error: Installation must be run as root"
    exit 1
fi

# Check dependencies
echo "Checking dependencies..."
if ! command -v clang &> /dev/null; then
    echo "Error: clang is required but not installed"
    exit 1
fi

if ! command -v llvm &> /dev/null; then
    echo "Error: llvm is required but not installed"
    exit 1
fi

# Build and install
echo "Building project..."
make clean
make all

echo "Installing binaries..."
make install

echo "Installation completed successfully!"
echo "You can now use:"
echo "  - sudo args_enforcer -c /etc/args_enforcer/default_policy.json"
echo "  - sudo policy_manager list"
echo "  - sudo stats_viewer"
EOF
    
    chmod +x "$src_package_dir/install.sh"
    
    # Create tarball
    cd "$BUILD_DIR"
    tar -czf "$DIST_DIR/${PACKAGE_NAME}-${VERSION}-src.tar.gz" "${PACKAGE_NAME}-${VERSION}-src"
    
    log_info "Source package created: ${PACKAGE_NAME}-${VERSION}-src.tar.gz"
}

# Create binary package
create_binary_package() {
    log_step "Creating binary package..."
    
    local bin_package_dir="$BUILD_DIR/${PACKAGE_NAME}-${VERSION}-bin"
    mkdir -p "$bin_package_dir/bin"
    mkdir -p "$bin_package_dir/configs"
    mkdir -p "$bin_package_dir/docs"
    mkdir -p "$bin_package_dir/tests"
    
    # Copy binaries
    cp "$PROJECT_DIR/bin/"* "$bin_package_dir/bin/"
    
    # Copy configurations
    cp "$PROJECT_DIR/configs/"*.json "$bin_package_dir/configs/"
    
    # Copy documentation
    cp "$PROJECT_DIR/README.md" "$bin_package_dir/"
    cp "$PROJECT_DIR/docs/"*.md "$bin_package_dir/docs/" 2>/dev/null || true
    cp "$PROJECT_DIR/LICENSE" "$bin_package_dir/" 2>/dev/null || true
    
    # Copy tests
    cp "$PROJECT_DIR/tests/"*.sh "$bin_package_dir/tests/"
    chmod +x "$bin_package_dir/tests/"*.sh
    
    # Create installation script for binary package
    cat > "$bin_package_dir/install.sh" << 'EOF'
#!/bin/bash
set -e

echo "Installing eBPF Process Control Tool (Binary Package)..."

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    echo "Error: Installation must be run as root"
    exit 1
fi

# Install binaries
echo "Installing binaries..."
cp bin/args_enforcer /usr/local/bin/
cp bin/policy_manager /usr/local/bin/
cp bin/stats_viewer /usr/local/bin/

# Set permissions
chmod +x /usr/local/bin/args_enforcer
chmod +x /usr/local/bin/policy_manager
chmod +x /usr/local/bin/stats_viewer

# Install configurations
echo "Installing configurations..."
mkdir -p /etc/args_enforcer
cp configs/default_policy.json /etc/args_enforcer/
cp configs/advanced_policies.json /etc/args_enforcer/

# Create log directory
mkdir -p /var/log/args_enforcer

echo "Installation completed successfully!"
echo "Configuration files are in: /etc/args_enforcer/"
echo "You can now use:"
echo "  - sudo args_enforcer -c /etc/args_enforcer/default_policy.json"
echo "  - sudo policy_manager -c /etc/args_enforcer/default_policy.json list"
echo "  - sudo stats_viewer"
EOF
    
    chmod +x "$bin_package_dir/install.sh"
    
    # Create uninstall script
    cat > "$bin_package_dir/uninstall.sh" << 'EOF'
#!/bin/bash
set -e

echo "Uninstalling eBPF Process Control Tool..."

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    echo "Error: Uninstallation must be run as root"
    exit 1
fi

# Stop any running processes
pkill -f args_enforcer || true

# Remove binaries
rm -f /usr/local/bin/args_enforcer
rm -f /usr/local/bin/policy_manager
rm -f /usr/local/bin/stats_viewer

# Ask about configuration removal
read -p "Remove configuration files? [y/N]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -rf /etc/args_enforcer
    echo "Configuration files removed."
fi

# Ask about log removal
read -p "Remove log files? [y/N]: " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -rf /var/log/args_enforcer
    echo "Log files removed."
fi

echo "Uninstallation completed!"
EOF
    
    chmod +x "$bin_package_dir/uninstall.sh"
    
    # Create tarball
    cd "$BUILD_DIR"
    tar -czf "$DIST_DIR/${PACKAGE_NAME}-${VERSION}-bin.tar.gz" "${PACKAGE_NAME}-${VERSION}-bin"
    
    log_info "Binary package created: ${PACKAGE_NAME}-${VERSION}-bin.tar.gz"
}

# Create DEB package (for Debian/Ubuntu)
create_deb_package() {
    log_step "Creating DEB package..."
    
    local deb_dir="$BUILD_DIR/${PACKAGE_NAME}_${VERSION}"
    mkdir -p "$deb_dir/DEBIAN"
    mkdir -p "$deb_dir/usr/local/bin"
    mkdir -p "$deb_dir/etc/args_enforcer"
    mkdir -p "$deb_dir/usr/share/doc/${PACKAGE_NAME}"
    mkdir -p "$deb_dir/var/log/args_enforcer"
    
    # Copy binaries
    cp "$PROJECT_DIR/bin/"* "$deb_dir/usr/local/bin/"
    
    # Copy configurations
    cp "$PROJECT_DIR/configs/"*.json "$deb_dir/etc/args_enforcer/"
    
    # Copy documentation
    cp "$PROJECT_DIR/README.md" "$deb_dir/usr/share/doc/${PACKAGE_NAME}/"
    cp "$PROJECT_DIR/docs/"*.md "$deb_dir/usr/share/doc/${PACKAGE_NAME}/" 2>/dev/null || true
    
    # Create control file
    cat > "$deb_dir/DEBIAN/control" << EOF
Package: ${PACKAGE_NAME}
Version: ${VERSION}
Section: security
Priority: optional
Architecture: amd64
Depends: libc6, libbpf0
Maintainer: eBPF Process Control Team <<EMAIL>>
Description: eBPF-based process control tool
 A state-of-the-art eBPF-based process control tool that enforces
 command-line argument policies in kernel space. This tool intercepts
 process execution and applies security policies to prevent dangerous
 command-line arguments from being executed.
EOF
    
    # Create postinst script
    cat > "$deb_dir/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e

echo "Configuring eBPF Process Control Tool..."

# Set proper permissions
chmod +x /usr/local/bin/args_enforcer
chmod +x /usr/local/bin/policy_manager
chmod +x /usr/local/bin/stats_viewer

# Create log directory with proper permissions
mkdir -p /var/log/args_enforcer
chmod 755 /var/log/args_enforcer

echo "eBPF Process Control Tool configured successfully!"
echo "Configuration files are in: /etc/args_enforcer/"
echo "You can now use:"
echo "  - sudo args_enforcer -c /etc/args_enforcer/default_policy.json"
echo "  - sudo policy_manager -c /etc/args_enforcer/default_policy.json list"
echo "  - sudo stats_viewer"
EOF
    
    chmod +x "$deb_dir/DEBIAN/postinst"
    
    # Create prerm script
    cat > "$deb_dir/DEBIAN/prerm" << 'EOF'
#!/bin/bash
set -e

echo "Stopping eBPF Process Control Tool..."

# Stop any running processes
pkill -f args_enforcer || true

echo "eBPF Process Control Tool stopped."
EOF
    
    chmod +x "$deb_dir/DEBIAN/prerm"
    
    # Build DEB package
    cd "$BUILD_DIR"
    if command -v dpkg-deb &> /dev/null; then
        dpkg-deb --build "${PACKAGE_NAME}_${VERSION}"
        mv "${PACKAGE_NAME}_${VERSION}.deb" "$DIST_DIR/"
        log_info "DEB package created: ${PACKAGE_NAME}_${VERSION}.deb"
    else
        log_warn "dpkg-deb not found, skipping DEB package creation"
    fi
}

# Create checksums
create_checksums() {
    log_step "Creating checksums..."
    
    cd "$DIST_DIR"
    for file in *.tar.gz *.deb 2>/dev/null; do
        if [[ -f "$file" ]]; then
            sha256sum "$file" >> checksums.sha256
        fi
    done
    
    if [[ -f checksums.sha256 ]]; then
        log_info "Checksums created: checksums.sha256"
    fi
}

# Main packaging function
main() {
    log_info "Starting packaging process for eBPF Process Control Tool v${VERSION}"
    
    clean_build
    build_project
    create_source_package
    create_binary_package
    create_deb_package
    create_checksums
    
    log_info "Packaging completed successfully!"
    echo
    echo "Created packages:"
    ls -la "$DIST_DIR"
    echo
    echo "Package contents:"
    echo "  - Source package: Complete source code with build system"
    echo "  - Binary package: Pre-compiled binaries with installation scripts"
    echo "  - DEB package: Debian/Ubuntu package for easy installation"
    echo "  - Checksums: SHA256 checksums for verification"
}

main "$@"
