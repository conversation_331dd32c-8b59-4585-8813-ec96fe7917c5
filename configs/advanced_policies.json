{"policies": [{"name": "ssh_security_enhanced", "description": "Enhanced SSH security with multiple checks", "cmdline": {"include": ["-o StrictHostKeyChecking=yes"], "exclude": ["-o StrictHostKeyChecking=no", "-o UserKnownHostsFile=/dev/null", "-o GlobalKnownHostsFile=/dev/null", "-o PasswordAuthentication=yes", "-o PubkeyAuthentication=no"]}}, {"name": "curl_security_strict", "description": "Strict curl security policies", "cmdline": {"include": [], "exclude": ["-k", "--insecure", "--disable-cert-revocation-checks", "--cacert /dev/null", "--capath /dev/null"]}}, {"name": "docker_security_comprehensive", "description": "Comprehensive Docker security enforcement", "cmdline": {"include": [], "exclude": ["--privileged", "--cap-add=ALL", "--cap-add=SYS_ADMIN", "--security-opt=no-new-privileges=false", "--security-opt=apparmor=unconfined", "--security-opt=seccomp=unconfined", "--user=root", "--net=host", "--pid=host", "--ipc=host"]}}, {"name": "sudo_restrictions_enhanced", "description": "Enhanced sudo usage restrictions", "cmdline": {"include": [], "exclude": ["-u root", "--preserve-env=PATH", "--preserve-env=LD_PRELOAD", "--preserve-env=LD_LIBRARY_PATH", "-E"]}}, {"name": "wget_security_strict", "description": "Strict wget security policies", "cmdline": {"include": [], "exclude": ["--no-check-certificate", "--no-verify-certificate", "--no-hsts", "--ca-certificate=/dev/null"]}}, {"name": "git_security", "description": "Git security policies to prevent dangerous operations", "cmdline": {"include": [], "exclude": ["-c http.sslVerify=false", "-c http.sslCAInfo=/dev/null", "--no-verify"]}}, {"name": "python_security", "description": "Python execution security policies", "cmdline": {"include": [], "exclude": ["-c exec(", "-c eval(", "-c __import__", "--disable-pip-version-check"]}}, {"name": "wildcard_demo", "description": "Demonstration of wildcard pattern matching", "cmdline": {"include": [], "exclude": ["--unsafe-*", "--debug-*", "--dev-*"]}}, {"name": "network_security", "description": "Network-related security policies", "cmdline": {"include": [], "exclude": ["--bind 0.0.0.0", "--listen 0.0.0.0", "--host 0.0.0.0", "--allow-root"]}}, {"name": "compiler_security", "description": "Compiler and build tool security", "cmdline": {"include": [], "exclude": ["-fno-stack-protector", "-fno-fortify-source", "-Wno-format-security", "--disable-pie"]}}]}