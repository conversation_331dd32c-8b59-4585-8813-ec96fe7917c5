{"policies": [{"name": "development_tools_security", "description": "Security policies for development tools", "cmdline": {"include": [], "exclude": ["--allow-run-as-root", "--disable-security", "--skip-verification"]}}, {"name": "network_tools_security", "description": "Security policies for network tools", "cmdline": {"include": [], "exclude": ["--no-ssl-verify", "--ignore-certificate-errors", "--disable-tls-verification"]}}, {"name": "package_manager_security", "description": "Security policies for package managers", "cmdline": {"include": [], "exclude": ["--allow-unauthenticated", "--force-unsafe-io", "--no-check-gpg"]}}, {"name": "compiler_security", "description": "Security policies for compilers", "cmdline": {"include": ["-fstack-protector"], "exclude": ["-fno-stack-protector", "-z execstack", "-Wl,-z,norelro"]}}, {"name": "database_security", "description": "Security policies for database tools", "cmdline": {"include": [], "exclude": ["--skip-ssl", "--disable-ssl-verify", "--allow-empty-password"]}}]}