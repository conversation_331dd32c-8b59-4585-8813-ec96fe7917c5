{"policies": [{"name": "ssh_security", "description": "Enforce secure SSH connections", "cmdline": {"include": ["-o StrictHostKeyChecking=yes"], "exclude": ["-o StrictHostKeyChecking=no", "-o UserKnownHostsFile=/dev/null", "-o GlobalKnownHostsFile=/dev/null"]}}, {"name": "curl_security", "description": "Prevent insecure curl usage", "cmdline": {"include": [], "exclude": ["-k", "--insecure", "--disable-cert-revocation-checks"]}}, {"name": "wget_security", "description": "Prevent insecure wget usage", "cmdline": {"include": [], "exclude": ["--no-check-certificate", "--no-verify-certificate"]}}, {"name": "docker_security", "description": "Enforce secure Docker practices", "cmdline": {"include": [], "exclude": ["--privileged", "--cap-add=ALL", "--security-opt=no-new-privileges=false"]}}, {"name": "sudo_restrictions", "description": "Restrict dangerous sudo usage", "cmdline": {"include": [], "exclude": ["-u root", "--preserve-env=PATH"]}}]}